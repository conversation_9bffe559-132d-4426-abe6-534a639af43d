/**
 * Database Initialization
 * Handles database setup, schema creation, and migration integration
 */

import { DatabasePool } from './connection';
import { SchemaManager } from './schema';
import { IdMappingManager } from '../utils/id-mapping';
import { GradientColumnManager } from '../utils/gradient-columns';
import { createMigrationIntegration } from '../../services/enhanced-realtime-sync.service';

/**
 * Database initialization manager
 * Coordinates the setup of database, schema, and related systems
 */
export class DatabaseInitializer {
  private db: any = null;
  private schemaManager: SchemaManager | null = null;
  private idMappingManager: IdMappingManager | null = null;
  private gradientColumnManager: GradientColumnManager | null = null;
  
  /**
   * Initialize database with optimized schema and connection pooling
   */
  async initDatabase(): Promise<any | null> {
    if (this.db) {
      return this.db;
    }

    try {
      // Get primary database connection
      const pool = DatabasePool.getInstance();
      this.db = await pool.getConnection();
      
      console.log('[DatabaseInitializer] Database connection established');
      
      // Initialize managers
      this.schemaManager = new SchemaManager(this.db);
      this.idMappingManager = new IdMappingManager(this.db);
      this.gradientColumnManager = new GradientColumnManager(this.db);
      
      // Verify and create schema
      await this.setupSchema();
      
      // Run migrations (currently disabled)
      await this.runMigrations();
      
      // Load ID mappings
      await this.idMappingManager.loadMappings();
      
      // Ensure gradient columns exist
      await this.gradientColumnManager.ensureGradientColumns();
      
      console.log('[DatabaseInitializer] Database initialization completed successfully');
      return this.db;
      
    } catch (error) {
      console.error('[DatabaseInitializer] Failed to initialize database:', error);
      this.db = null;
      return null;
    }
  }
  
  /**
   * Setup database schema
   */
  private async setupSchema(): Promise<void> {
    if (!this.schemaManager) {
      throw new Error('Schema manager not initialized');
    }
    
    // Check if all required tables exist
    const { existingTables, missingTables, needsCompleteSchema } = await this.schemaManager.verifyRequiredTables();
    
    if (missingTables.length > 0) {
      console.log('[DatabaseInitializer] Missing tables:', missingTables);
      
      if (needsCompleteSchema) {
        console.log('[DatabaseInitializer] Core tables missing, creating complete schema...');
        await this.schemaManager.createCompleteSchema();
      } else if (missingTables.some(table => ['organizations', 'organization_members', 'users'].includes(table))) {
        console.log('[DatabaseInitializer] Organization/user tables missing, creating them...');
        await this.schemaManager.ensureOrganizationTables();
      }
    } else {
      console.log('[DatabaseInitializer] All required tables exist');
      
      // Verify table schemas are correct
      await this.schemaManager.verifyAndUpdateSchemas();
    }
  }
  
  /**
   * Run database migrations
   */
  private async runMigrations(): Promise<void> {
    // TODO: Temporarily disabled enterprise migration system to fix immediate DB loading issue
    // Will re-enable after fixing the async/sync issue
    try {
      console.log('[DatabaseInitializer] Enterprise migration system temporarily disabled');

      // const migrationHelper = createMigrationIntegration();
      // const migrationSuccess = await migrationHelper.runEnhancedMigrations();

      // if (!migrationSuccess) {
      //   console.error('[DatabaseInitializer] Enterprise migration failed - continuing with existing schema');
      //   // Don't throw here - allow the app to continue with existing schema
      // } else {
      //   console.log('[DatabaseInitializer] Enterprise migration completed successfully');
      // }
    } catch (error) {
      console.error('[DatabaseInitializer] Migration failed:', error);
      // Don't throw here - allow the app to continue with existing schema
    }
  }
  
  /**
   * Get the initialized database instance
   */
  getDatabase(): any {
    return this.db;
  }
  
  /**
   * Get schema manager instance
   */
  getSchemaManager(): SchemaManager | null {
    return this.schemaManager;
  }
  
  /**
   * Get ID mapping manager instance
   */
  getIdMappingManager(): IdMappingManager | null {
    return this.idMappingManager;
  }
  
  /**
   * Get gradient column manager instance
   */
  getGradientColumnManager(): GradientColumnManager | null {
    return this.gradientColumnManager;
  }
  
  /**
   * Close database and cleanup resources
   */
  async close(): Promise<void> {
    if (this.db) {
      try {
        // Release the connection back to pool
        const pool = DatabasePool.getInstance();
        pool.releaseConnection(this.db);
        
        // Close all pool connections
        pool.closeAll();
        
        this.db = null;
        this.schemaManager = null;
        this.idMappingManager = null;
        this.gradientColumnManager = null;
        
        console.log('[DatabaseInitializer] Database closed successfully');
      } catch (error) {
        console.error('[DatabaseInitializer] Error closing database:', error);
      }
    }
  }
  
  /**
   * Get database health status
   */
  async getHealthStatus(): Promise<{
    isConnected: boolean;
    tablesExist: boolean;
    canQuery: boolean;
    poolStats: any;
  }> {
    try {
      const isConnected = this.db !== null;
      
      let tablesExist = false;
      let canQuery = false;
      
      if (isConnected && this.schemaManager) {
        try {
          const { existingTables } = await this.schemaManager.verifyRequiredTables();
          tablesExist = existingTables.length > 0;
          
          // Test a simple query
          this.db.prepare('SELECT 1').get();
          canQuery = true;
        } catch (error) {
          console.warn('[DatabaseInitializer] Health check query failed:', error);
        }
      }
      
      const pool = DatabasePool.getInstance();
      const poolStats = pool.getStats();
      
      return {
        isConnected,
        tablesExist,
        canQuery,
        poolStats
      };
    } catch (error) {
      console.error('[DatabaseInitializer] Health check failed:', error);
      return {
        isConnected: false,
        tablesExist: false,
        canQuery: false,
        poolStats: null
      };
    }
  }
}

// Singleton instance
let databaseInitializer: DatabaseInitializer | null = null;

/**
 * Get singleton database initializer instance
 */
export function getDatabaseInitializer(): DatabaseInitializer {
  if (!databaseInitializer) {
    databaseInitializer = new DatabaseInitializer();
  }
  return databaseInitializer;
}

/**
 * Initialize database (convenience function)
 */
export async function initDatabase(): Promise<any | null> {
  const initializer = getDatabaseInitializer();
  return await initializer.initDatabase();
}

/**
 * Get initialized database instance (convenience function)
 */
export function getDatabase(): any {
  const initializer = getDatabaseInitializer();
  return initializer.getDatabase();
}
